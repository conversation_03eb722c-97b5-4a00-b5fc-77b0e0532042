const express = require('express');
const mysql = require('mysql2/promise');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.API_PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
};

// Test database connection
async function testConnection() {
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connected successfully');
    await connection.end();
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
  }
}

// Get all parking spots with restrictions and zone information
app.get('/api/parking-spots', async (req, res) => {
  try {
    const { lat, lng, radius = 0.01 } = req.query;
    
    const connection = await mysql.createConnection(dbConfig);
    
    let query = `
      SELECT 
        pb.kerbside_id,
        pb.latitude,
        pb.longitude,
        pb.road_segment_description,
        pbs.status_sign,
        pbs.status_timestamp,
        pbs.zone_id,
        pzs.on_street,
        psp.restriction_start_time,
        psp.restriction_finish_time,
        psp.max_stay_minutes,
        psp.restriction_display,
        psp.restriction_day
      FROM PARKING_BAY pb
      LEFT JOIN PARKING_BAY_SENSOR pbs ON pb.kerbside_id = pbs.kerbside_id
      LEFT JOIN PARKING_ZONE_SEG pzs ON pbs.zone_id = pzs.zone_id
      LEFT JOIN PARKING_SIGN_PLATE psp ON pbs.zone_id = psp.zone_id
    `;
    
    let params = [];
    
    // Add location filtering if coordinates provided
    if (lat && lng) {
      query += ` WHERE pb.latitude BETWEEN ? AND ? AND pb.longitude BETWEEN ? AND ?`;
      params = [
        parseFloat(lat) - parseFloat(radius),
        parseFloat(lat) + parseFloat(radius),
        parseFloat(lng) - parseFloat(radius),
        parseFloat(lng) + parseFloat(radius)
      ];
    }
    
    query += ` ORDER BY pb.kerbside_id`;
    
    const [rows] = await connection.execute(query, params);
    await connection.end();
    
    // Transform data to match iOS expectations
    const transformedData = rows.map(row => ({
      id: row.kerbside_id,
      kerbsideId: row.kerbside_id,
      name: `Parking Spot ${row.kerbside_id}`,
      address: row.road_segment_description || 'N/A',
      latitude: row.latitude,
      longitude: row.longitude,
      zoneId: row.zone_id,
      onStreet: row.on_street,
      isOccupied: row.status_sign === 1,
      lastUpdated: row.status_timestamp,
      restrictionStartTime: row.restriction_start_time,
      restrictionFinishTime: row.restriction_finish_time,
      maxStayMinutes: row.max_stay_minutes,
      restrictionDisplay: row.restriction_display,
      restrictionDay: row.restriction_day,
      // Legacy fields for compatibility
      price: '$0', // Not available in current database
      spots: '1'   // Each kerbside_id represents one spot
    }));
    
    res.json(transformedData);
  } catch (error) {
    console.error('Error fetching parking spots:', error);
    res.status(500).json({ error: 'Failed to fetch parking spots' });
  }
});

// Get specific parking spot details
app.get('/api/parking-spots/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const connection = await mysql.createConnection(dbConfig);
    
    const query = `
      SELECT 
        pb.kerbside_id,
        pb.latitude,
        pb.longitude,
        pb.road_segment_description,
        pbs.status_sign,
        pbs.status_timestamp,
        pbs.zone_id,
        pzs.on_street,
        pzs.street_from,
        pzs.street_to,
        psp.restriction_start_time,
        psp.restriction_finish_time,
        psp.max_stay_minutes,
        psp.restriction_display,
        psp.restriction_day
      FROM PARKING_BAY pb
      LEFT JOIN PARKING_BAY_SENSOR pbs ON pb.kerbside_id = pbs.kerbside_id
      LEFT JOIN PARKING_ZONE_SEG pzs ON pbs.zone_id = pzs.zone_id
      LEFT JOIN PARKING_SIGN_PLATE psp ON pbs.zone_id = psp.zone_id
      WHERE pb.kerbside_id = ?
    `;
    
    const [rows] = await connection.execute(query, [id]);
    await connection.end();
    
    if (rows.length === 0) {
      return res.status(404).json({ error: 'Parking spot not found' });
    }
    
    const row = rows[0];
    const transformedData = {
      id: row.kerbside_id,
      kerbsideId: row.kerbside_id,
      name: `Parking Spot ${row.kerbside_id}`,
      address: row.road_segment_description || 'N/A',
      latitude: row.latitude,
      longitude: row.longitude,
      zoneId: row.zone_id,
      onStreet: row.on_street,
      streetFrom: row.street_from,
      streetTo: row.street_to,
      isOccupied: row.status_sign === 1,
      lastUpdated: row.status_timestamp,
      restrictionStartTime: row.restriction_start_time,
      restrictionFinishTime: row.restriction_finish_time,
      maxStayMinutes: row.max_stay_minutes,
      restrictionDisplay: row.restriction_display,
      restrictionDay: row.restriction_day,
      price: '$0',
      spots: '1'
    };
    
    res.json(transformedData);
  } catch (error) {
    console.error('Error fetching parking spot details:', error);
    res.status(500).json({ error: 'Failed to fetch parking spot details' });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 XYZ Parking API server running on port ${PORT}`);
  console.log(`📱 iPhone can access at: http://*************:${PORT}/api`);
  testConnection();
});

#!/bin/bash

echo "🧪 Testing XYZ Parking API..."

# Test health endpoint
echo "1. Testing health endpoint..."
curl -s "http://localhost:3000/api/health" | jq .

echo -e "\n2. Testing parking spots endpoint (limited)..."
curl -s "http://localhost:3000/api/parking-spots?lat=-37.8136&lng=144.9631&radius=0.01" | jq '. | length'

echo -e "\n3. Testing specific parking spot..."
curl -s "http://localhost:3000/api/parking-spots/51614" | jq '.name, .onStreet, .isOccupied, .restrictionDisplay'

echo -e "\n4. Testing parking spots with restrictions..."
curl -s "http://localhost:3000/api/parking-spots" | jq '[.[] | select(.restrictionDisplay != null)] | .[0:3] | .[] | {name, onStreet, restrictionDisplay, restrictionDay, maxStayMinutes}'

echo -e "\n✅ API tests completed!"

//
//  DataService.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import Foundation
import CoreLocation

struct ParkingLocation: Identifiable, Decodable, Hashable {
    let id: String
    let kerbsideId: String
    let name: String
    let address: String
    let price: String
    let spots: String
    let latitude: Double
    let longitude: Double

    // New fields from MySQL database
    let zoneId: String?
    let onStreet: String?
    let isOccupied: Bool
    let lastUpdated: String?
    let restrictionStartTime: String?
    let restrictionFinishTime: String?
    let maxStayMinutes: Int?
    let restrictionDisplay: String?
    let restrictionDay: String?

    // Custom coding keys to handle API response
    enum CodingKeys: String, CodingKey {
        case id, kerbsideId, name, address, price, spots, latitude, longitude
        case zoneId, onStreet, isOccupied, lastUpdated
        case restrictionStartTime, restrictionFinishTime, maxStayMinutes
        case restrictionDisplay, restrictionDay
    }

    // MARK: - Distance Calculation
    func distance(from userLocation: CLLocation) -> CLLocationDistance {
        let parkingLocation = CLLocation(latitude: latitude, longitude: longitude)
        return userLocation.distance(from: parkingLocation)
    }

    func formattedDistance(from userLocation: CLLocation?) -> String {
        guard let userLocation = userLocation else {
            return "-- km"
        }

        let distanceInMeters = distance(from: userLocation)

        if distanceInMeters < 1000 {
            return String(format: "%.0f m", distanceInMeters)
        } else {
            let distanceInKm = distanceInMeters / 1000
            return String(format: "%.1f km", distanceInKm)
        }
    }

    // MARK: - Parking Status
    var statusText: String {
        return isOccupied ? "Occupied" : "Available"
    }

    var statusColor: String {
        return isOccupied ? "red" : "green"
    }

    // MARK: - Restriction Information
    var restrictionText: String {
        guard let display = restrictionDisplay,
              let startTime = restrictionStartTime,
              let endTime = restrictionFinishTime,
              let day = restrictionDay else {
            return "No restrictions"
        }

        let timeRange = "\(formatTime(startTime))-\(formatTime(endTime))"
        let dayText = formatDay(day)

        return "\(display) \(dayText) \(timeRange)"
    }

    var maxStayText: String {
        guard let minutes = maxStayMinutes, minutes > 0 else {
            return "No time limit"
        }

        if minutes >= 60 {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            if remainingMinutes == 0 {
                return "\(hours)h max"
            } else {
                return "\(hours)h \(remainingMinutes)m max"
            }
        } else {
            return "\(minutes)m max"
        }
    }

    // MARK: - Helper Methods
    func formatTime(_ time: String) -> String {
        // Convert "07:30:00" to "7:30 AM"
        let components = time.split(separator: ":")
        guard components.count >= 2,
              let hour = Int(components[0]),
              let minute = Int(components[1]) else {
            return time
        }

        let period = hour >= 12 ? "PM" : "AM"
        let displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour)

        if minute == 0 {
            return "\(displayHour) \(period)"
        } else {
            return "\(displayHour):\(String(format: "%02d", minute)) \(period)"
        }
    }

    func formatDay(_ day: String) -> String {
        switch day.lowercased() {
        case "mon-fri":
            return "Mon-Fri"
        case "mon-sat":
            return "Mon-Sat"
        case "mon-sun":
            return "Mon-Sun"
        case "sat":
            return "Sat"
        case "sun":
            return "Sun"
        default:
            return day.capitalized
        }
    }
}

class DataService: ObservableObject {
    static let shared = DataService()

    private let baseURL = "http://192.168.0.206:3000/api"

    @Published var parkingLocations: [ParkingLocation] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    private init() {}

    // MARK: - Network Methods

    func fetchParkingLocations(latitude: Double? = nil, longitude: Double? = nil, radius: Double = 0.02) {
        isLoading = true
        errorMessage = nil

        var urlString = "\(baseURL)/parking-spots"

        // Add location parameters if provided
        if let lat = latitude, let lng = longitude {
            urlString += "?lat=\(lat)&lng=\(lng)&radius=\(radius)"
        }

        guard let url = URL(string: urlString) else {
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "Invalid URL"
            }
            return
        }

        print("🌐 Fetching parking data from API...")
        URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            DispatchQueue.main.async {
                self?.isLoading = false

                if let error = error {
                    self?.errorMessage = "Network error: \(error.localizedDescription)"
                    return
                }

                guard let data = data else {
                    self?.errorMessage = "No data received"
                    return
                }

                do {
                    let decoder = JSONDecoder()
                    let locations = try decoder.decode([ParkingLocation].self, from: data)
                    self?.parkingLocations = locations
                    print("✅ Loaded \(locations.count) parking locations from API")
                } catch {
                    self?.errorMessage = "Failed to decode data: \(error.localizedDescription)"
                    print("❌ Decoding error: \(error)")
                }
            }
        }.resume()
    }

    func fetchParkingLocationDetail(id: String, completion: @escaping (ParkingLocation?) -> Void) {
        let urlString = "\(baseURL)/parking-spots/\(id)"

        guard let url = URL(string: urlString) else {
            completion(nil)
            return
        }

        URLSession.shared.dataTask(with: url) { data, response, error in
            guard let data = data, error == nil else {
                DispatchQueue.main.async {
                    completion(nil)
                }
                return
            }

            do {
                let decoder = JSONDecoder()
                let location = try decoder.decode(ParkingLocation.self, from: data)
                DispatchQueue.main.async {
                    completion(location)
                }
            } catch {
                print("❌ Failed to decode parking location detail: \(error)")
                DispatchQueue.main.async {
                    completion(nil)
                }
            }
        }.resume()
    }

    // MARK: - Legacy Support (for fallback)

    static func load<T: Decodable>(_ filename: String) -> T {
        let data: Data

        guard let file = Bundle.main.url(forResource: filename, withExtension: nil)
        else {
            fatalError("Couldn't find \(filename) in main bundle.")
        }

        do {
            data = try Data(contentsOf: file)
        } catch {
            fatalError("Couldn't load \(filename) from main bundle:\n\(error)")
        }

        do {
            let decoder = JSONDecoder()
            return try decoder.decode(T.self, from: data)
        } catch {
            fatalError("Couldn't parse \(filename) as \(T.self):\n\(error)")
        }
    }
}

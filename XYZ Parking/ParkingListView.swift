//
//  ParkingListView.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI
import CoreLocation

struct ParkingListView: View {
    let parkingLocations: [ParkingLocation]
    let searchText: String
    let userLocation: CLLocation?
    @Binding var selectedParkingFromMap: ParkingLocation?
    let onParkingLocationSelected: (ParkingLocation) -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // Section title
            HStack {
                Text("Parking Nearby")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundStyle(.primary)
                Spacer()
            }
            .padding(.horizontal, 20)

            // Parking list
            LazyVStack(spacing: 16) {
                if parkingLocations.isEmpty {
                    VStack(spacing: 12) {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 40))
                            .foregroundStyle(.secondary)
                        Text("No parking locations found")
                            .font(.headline)
                            .foregroundStyle(.secondary)
                        Text("Try adjusting your search terms")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    .padding(.top, 40)
                } else {
                    ForEach(Array(parkingLocations.enumerated()), id: \.element.id) { index, location in
                        ParkingLocationCard(
                            parkingLocation: location,
                            userLocation: userLocation,
                            isTopCard: index == 0,
                            onTap: {
                                // 直接调用回调，不再使用导航
                                onParkingLocationSelected(location)
                            }
                        )
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
    }
}

// MARK: - Preview
#Preview {
    ParkingListView(
        parkingLocations: [
            ParkingLocation(
                id: "51614",
                kerbsideId: "51614",
                name: "Parking Spot 51614",
                address: "Collins Street between Spring Street and Exhibition Street",
                price: "$0",
                spots: "1",
                latitude: -37.8136,
                longitude: 144.9631,
                zoneId: "7303",
                onStreet: "Collins Street",
                isOccupied: false,
                lastUpdated: "2025-03-25T11:09:20.000Z",
                restrictionStartTime: "07:30:00",
                restrictionFinishTime: "18:30:00",
                maxStayMinutes: 120,
                restrictionDisplay: "2P",
                restrictionDay: "mon-fri"
            ),
            ParkingLocation(
                id: "17954",
                kerbsideId: "17954",
                name: "Parking Spot 17954",
                address: "Bourke Street between Queen Street and King Street",
                price: "$0",
                spots: "1",
                latitude: -37.8140,
                longitude: 144.9633,
                zoneId: "7265",
                onStreet: "Bourke Street",
                isOccupied: true,
                lastUpdated: "2025-03-25T10:56:53.000Z",
                restrictionStartTime: "07:30:00",
                restrictionFinishTime: "18:30:00",
                maxStayMinutes: 60,
                restrictionDisplay: "1P",
                restrictionDay: "mon-sat"
            )
        ],
        searchText: "",
        userLocation: CLLocation(latitude: -37.8140, longitude: 144.9633),
        selectedParkingFromMap: .constant(nil),
        onParkingLocationSelected: { _ in }
    )
}
